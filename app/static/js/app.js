// Add this status manager to handle execution status updates
const ExecutionStatusManager = {
  panel: null,
  progressBar: null,
  currentLabel: null,
  progressText: null,
  logContainer: null,
  totalActions: 0,
  currentAction: 0,
  isRunning: false,

  initialize() {
    this.panel = document.getElementById('execution-status-panel');
    this.progressBar = document.getElementById('execution-progress-bar');
    this.currentLabel = document.getElementById('current-action-label');
    this.progressText = document.getElementById('progress-text');
    this.logContainer = document.getElementById('execution-log');

    // Set up minimize button
    document.getElementById('minimize-status-panel').addEventListener('click', () => {
      this.panel.classList.toggle('minimized');
    });
  },

  start(totalActions) {
    this.totalActions = totalActions;
    this.currentAction = 0;
    this.isRunning = true;
    this.panel.style.display = 'block';
    this.panel.classList.remove('minimized');
    this.progressBar.style.width = '0%';
    this.currentLabel.textContent = 'Starting execution...';
    this.progressText.textContent = `0/${totalActions}`;
    this.logContainer.innerHTML = '';
    this.addLogEntry('Execution started', 'info');
  },

  update(actionIndex, status, message) {
    if (!this.isRunning) return;

    this.currentAction = actionIndex + 1;
    const percent = Math.round((this.currentAction / this.totalActions) * 100);

    this.progressBar.style.width = `${percent}%`;
    this.currentLabel.textContent = message || `Executing action ${this.currentAction}`;
    this.progressText.textContent = `${this.currentAction}/${this.totalActions}`;

    this.addLogEntry(`Action ${actionIndex + 1}: ${message || status}`, status);

    // Auto-scroll log to bottom
    this.logContainer.scrollTop = this.logContainer.scrollHeight;
  },

  complete(success, message) {
    this.isRunning = false;
    this.progressBar.style.width = '100%';
    this.currentLabel.textContent = success ? 'Execution completed' : 'Execution failed';
    this.addLogEntry(message || (success ? 'All actions completed successfully' : 'Execution failed'),
                     success ? 'success' : 'error');

    // Keep panel visible for 10 seconds after completion
    setTimeout(() => {
      if (!this.isRunning) {
        this.panel.classList.add('minimized');
      }
    }, 10000);
  },

  addLogEntry(message, type) {
    const entry = document.createElement('div');
    entry.className = `log-entry ${type || 'info'}`;
    entry.innerHTML = `<span class="log-time">[${new Date().toLocaleTimeString()}]</span> ${message}`;
    this.logContainer.appendChild(entry);
  }
};

/**
 * Enhance tab navigation with modern interactions
 */
function enhanceTabNavigation() {
  // Get all tab navigation elements
  const tabNavigations = document.querySelectorAll('.nav-tabs');

  tabNavigations.forEach(navTabs => {
    const tabId = navTabs.id;
    const tabButtons = navTabs.querySelectorAll('.nav-link');

    // Create a function to update the sliding indicator
    const updateSliderPosition = (activeTab) => {
      // Get position of active tab
      const rect = activeTab.getBoundingClientRect();
      const navRect = navTabs.getBoundingClientRect();

      // Calculate positions relative to the nav container
      const left = rect.left - navRect.left;
      const width = rect.width;

      // Style the indicator
      navTabs.style.setProperty('--slider-left', `${left}px`);
      navTabs.style.setProperty('--slider-width', `${width}px`);

      // Adding a custom style element for this specific nav
      if (!document.getElementById(`slider-style-${tabId}`)) {
        const style = document.createElement('style');
        style.id = `slider-style-${tabId}`;
        style.innerHTML = `
          #${tabId}::after {
            transform: translateX(var(--slider-left, 0));
            width: var(--slider-width, 0);
          }
        `;
        document.head.appendChild(style);
      }
    };

    // Add hover effects and active indicator
    tabButtons.forEach(button => {
      // Add ripple effect on click
      button.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();

        ripple.className = 'tab-ripple';
        ripple.style.left = `${e.clientX - rect.left}px`;
        ripple.style.top = `${e.clientY - rect.top}px`;

        button.appendChild(ripple);

        // Remove ripple after animation completes
        setTimeout(() => {
          ripple.remove();
        }, 600);

        // Update sliding indicator position
        updateSliderPosition(button);

        // Track current active tab in localStorage for persistence
        if (tabId) {
          localStorage.setItem(`last-active-tab-${tabId}`, button.id);
        }
      });
    });

    // Restore last active tab on page load
    if (tabId) {
      const lastActiveTabId = localStorage.getItem(`last-active-tab-${tabId}`);
      if (lastActiveTabId) {
        const tabToActivate = document.getElementById(lastActiveTabId);
        if (tabToActivate) {
          // Use Bootstrap's tab API to activate the tab
          const tabInstance = new bootstrap.Tab(tabToActivate);
          tabInstance.show();

          // Update slider position after tab is shown
          setTimeout(() => {
            updateSliderPosition(tabToActivate);
          }, 150);
        }
      } else {
        // Find the active tab and position the indicator
        const activeTab = navTabs.querySelector('.nav-link.active');
        if (activeTab) {
          setTimeout(() => {
            updateSliderPosition(activeTab);
          }, 150);
        }
      }
    }

    // Handle window resize to reposition the indicator
    window.addEventListener('resize', () => {
      const activeTab = navTabs.querySelector('.nav-link.active');
      if (activeTab) {
        updateSliderPosition(activeTab);
      }
    });
  });

  // Add CSS for ripple effect if not already present
  if (!document.getElementById('tab-ripple-style')) {
    const style = document.createElement('style');
    style.id = 'tab-ripple-style';
    style.innerHTML = `
      .tab-ripple {
        position: absolute;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: tab-ripple 0.6s linear;
        pointer-events: none;
      }

      @keyframes tab-ripple {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }

      .nav-link {
        position: relative;
        overflow: hidden;
      }
    `;
    document.head.appendChild(style);
  }

  console.log('Tab navigation enhanced with modern interactions');
}

// Initialize event handlers when document is loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('App.js loaded, initializing app components...');

  // Initialize execution status manager
  ExecutionStatusManager.initialize();

  // Enhance tab navigation
  enhanceTabNavigation();

  // Only set up socket handlers if socket exists
  if (typeof socket !== 'undefined' && socket) {
    // Update the socket.io handler for action results
    socket.on('action_result', function(data) {
      // Only update the UI, don't modify row classes
      if (data.status === 'started') {
        ExecutionStatusManager.start(data.total_actions || 0);
      }
      else if (data.status === 'running') {
        ExecutionStatusManager.update(
          data.action_index || 0,
          'info',
          data.message || `Executing action ${data.action_index + 1}`
        );
        // Log action status for running actions
        if (data.action_id && window.mainApp && window.mainApp.logActionStatus) {
          window.mainApp.logActionStatus(data.action_id, 'running');
        }
      }
      else if (data.status === 'success') {
        if (data.completed) {
          ExecutionStatusManager.complete(true, data.message);
        } else {
          // Special handling for hook recovery success
          if (data.hook_recovery_success) {
            // Find the action item and explicitly update its classes
            const actionItem = document.querySelector(`.action-item[data-action-index="${data.action_index}"]`);
            if (actionItem) {
              // First remove error classes
              actionItem.classList.remove('error', 'action-item-failed', 'action-item-recovery', 'action-item-retrying');
              // Then add success class
              actionItem.classList.add('success');
              console.log(`Hook recovery success: Updated classes for action ${data.action_index}`);
            }
          }

          // Store screenshot_filename in the action data if available
          if (data.screenshot_filename) {
            console.log(`Received screenshot_filename: ${data.screenshot_filename} for action ${data.action_index}`);
            // Find the action item
            const actionItem = document.querySelector(`.action-item[data-action-index="${data.action_index}"]`);
            if (actionItem) {
              // Store the screenshot_filename as a data attribute
              actionItem.setAttribute('data-screenshot-filename', data.screenshot_filename);
              console.log(`Stored screenshot_filename for action ${data.action_index}`);
            }

            // Also store in the current actions array if it exists
            if (window.app && window.app.currentActions && window.app.currentActions[data.action_index]) {
              window.app.currentActions[data.action_index].screenshot_filename = data.screenshot_filename;
              console.log(`Stored screenshot_filename in currentActions[${data.action_index}]`);
            }
          }

          ExecutionStatusManager.update(
            data.action_index || 0,
            'success',
            data.message || 'Action completed successfully'
          );
          // Log action status for successful actions
          if (data.action_id && window.mainApp && window.mainApp.logActionStatus) {
            window.mainApp.logActionStatus(data.action_id, 'pass');
          }
        }
      }
      else if (data.status === 'error' || data.status === 'failed') {
        if (data.completed) {
          ExecutionStatusManager.complete(false, data.message);
        } else {
          ExecutionStatusManager.update(
            data.action_index || 0,
            'error',
            data.message || 'Action failed'
          );
          // Log action status for failed actions
          if (data.action_id && window.mainApp && window.mainApp.logActionStatus) {
            window.mainApp.logActionStatus(data.action_id, 'fail');
          }
        }
      }
      else if (data.status === 'stopped') {
        ExecutionStatusManager.complete(false, data.message || 'Execution stopped');
      }

      // Always refresh the screenshot if available
      if (data.screenshot) {
        refreshScreenshot();
      }
    });

    // Handle pong response
    socket.on('pong', function(data) {
      // Connection is alive
      if (SocketHealthMonitor.connectionLost) {
        console.log('Connection restored');
        SocketHealthMonitor.connectionLost = false;
      }
    });
  } else {
    console.error('Socket not available in app.js');
  }

  // Initialize socket listeners if socket exists
  if (typeof socket !== 'undefined') {
    setupAppSocketListeners(socket);
  } else {
    console.error('Socket.IO connection not available');
  }

  // Initialize execution manager
  if (typeof window.initExecutionManager === 'function') {
    window.initExecutionManager(socket);
  }

  // Setup device screen functionality
  setupDeviceScreen();

  console.log('App initialization complete');
});

/**
 * Set up Socket.IO listeners specific to this app module
 */
function setupAppSocketListeners(socket) {
  // Add app-specific socket event listeners here

  // Example: Listen for server notifications
  socket.on('server_notification', function(data) {
    if (typeof showToast === 'function') {
      showToast('Server', data.message, data.type || 'info');
    } else {
      console.log('Server notification:', data.message);
    }
  });

  console.log('App socket listeners initialized');
}

/**
 * Set up functionality for the device screen
 */
function setupDeviceScreen() {
  const deviceScreen = document.getElementById('deviceScreen');
  const refreshBtn = document.getElementById('refreshScreenBtn');

  if (refreshBtn) {
    refreshBtn.addEventListener('click', function() {
      if (typeof socket !== 'undefined') {
        console.log('Requesting screenshot refresh');
        socket.emit('refresh_screenshot');

        if (typeof showLoading === 'function') {
          showLoading(true, 'Refreshing screenshot...');
        }
      }
    });
  }

  console.log('Device screen functionality initialized');
}

// Make functions available globally
window.setupAppSocketListeners = setupAppSocketListeners;
window.setupDeviceScreen = setupDeviceScreen;

// WebSocket health monitor
const SocketHealthMonitor = {
  pingInterval: null,
  connectionLost: false,

  start() {
    // Clear any existing interval
    this.stop();

    // Only start if socket exists
    if (typeof socket === 'undefined' || !socket) {
      console.error('Cannot start health monitor - socket not available');
      return;
    }

    // Start ping every 5 seconds
    this.pingInterval = setInterval(() => {
      socket.emit('ping', {}, (response) => {
        if (this.connectionLost) {
          console.log('Connection restored');
          this.connectionLost = false;

          // If execution was in progress, request status update
          if (ExecutionStatusManager.isRunning) {
            socket.emit('check_execution_status');
          }
        }
      });

      // Set a timeout to detect missed pongs
      setTimeout(() => {
        if (!this.connectionLost) {
          console.log('WebSocket connection may be lost');
          this.connectionLost = true;

          // Show reconnection message
          if (ExecutionStatusManager.isRunning) {
            ExecutionStatusManager.addLogEntry('Connection lost, attempting to reconnect...', 'warning');
          }
        }
      }, 4000);
    }, 5000);
  },

  stop() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }
};

// Initialize the socket health monitor when document is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Only start health monitor if socket exists
  if (typeof socket !== 'undefined' && socket) {
    SocketHealthMonitor.start();
  } else {
    console.error('Cannot start health monitor - socket not available');
  }
});

// Revised version for safer socket usage
function executeAction(index) {
  // Check if socket exists before using it
  if (typeof socket !== 'undefined' && socket) {
    // Execute the action without modifying UI row status
    socket.emit('execute_action', { index: index });
  } else {
    console.error('Cannot execute action - socket not available');
  }
}

// Add this functionality to handle action log updates
socket.on('action_log', function(data) {
    var logContainer = document.getElementById('action-log');
    if (!logContainer) return;

    var entry = document.createElement('div');
    entry.className = 'log-entry ' + (data.type || 'info');

    var time = new Date().toLocaleTimeString();
    entry.innerHTML = '<span class="log-time">[' + time + ']</span> ' + data.message;

    logContainer.appendChild(entry);

    // Auto-scroll to bottom
    logContainer.scrollTop = logContainer.scrollHeight;
});

// Helper function to get descriptions for each action
function get_action_description(action) {
    switch (action.type) {
        case 'tap':
            return `Tapping at (${action.x}, ${action.y})`;
        case 'swipe':
            return `Swiping from (${action.start_x}, ${action.start_y}) to (${action.end_x}, ${action.end_y})`;
        case 'text':
            return `Entering text: "${action.text.substring(0, 15)}${action.text.length > 15 ? '...' : ''}"`;
        case 'wait':
            return `Waiting for ${action.duration} seconds`;
        case 'key':
            return `Pressing key: ${action.key}`;
        case 'hideKeyboard':
            return `Hiding keyboard`;
        case 'airplaneMode':
            return `${action.enabled ? 'Enable' : 'Disable'} Airplane Mode`;
        case 'addMedia':
            const mediaName = action.file_path?.split('/').pop() || 'file';
            return `Add Media: ${mediaName}`;
        case 'deviceBack':
            return `Press Android Back`;
        case 'getValue':
            return `Get Value: ${action.locator_type}`;
        case 'compareValue':
            return `Compare Value: ${action.locator_type}`;
        default:
            return `${action.type} action`;
    }
}

// Make sure socket.io is initialized with the correct base URL
const socket = io.connect(window.location.origin, {
  path: '/socket.io',
  transports: ['websocket', 'polling']
});