import sqlite3
import os

# --- Configuration ---
# Adjust this path if your database is located elsewhere relative to your project root
DB_NAME = 'test_execution.db'
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Assumes this script is in a subdir of project root
DB_PATH = os.path.join(PROJECT_ROOT, 'app', 'data', DB_NAME)
# If your DB_PATH is different (e.g., directly in the project root or in app/data), adjust accordingly.
# Example: DB_PATH = os.path.join(PROJECT_ROOT, DB_NAME)
# Example: DB_PATH = os.path.join(PROJECT_ROOT, 'app', 'data', DB_NAME)

def clear_test_steps():
    """Connects to the SQLite database and clears the test_steps table."""
    conn = None  # Initialize conn to None
    try:
        print(f"Attempting to connect to database at: {DB_PATH}")
        if not os.path.exists(DB_PATH):
            print(f"ERROR: Database file not found at {DB_PATH}")
            print("Please ensure the DB_PATH variable in the script is correct.")
            return

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check current number of rows (optional)
        cursor.execute("SELECT COUNT(*) FROM test_steps")
        count_before = cursor.fetchone()[0]
        print(f"Found {count_before} rows in test_steps before clearing.")

        # Clear the test_steps table
        cursor.execute("DELETE FROM test_steps")
        conn.commit()

        # Verify (optional)
        cursor.execute("SELECT COUNT(*) FROM test_steps")
        count_after = cursor.fetchone()[0]
        print(f"Found {count_after} rows in test_steps after clearing.")

        if count_before > 0 and count_after == 0:
            print("Successfully cleared the test_steps table.")
        elif count_before == 0:
            print("test_steps table was already empty.")
        else:
            print("Something went wrong, test_steps table was not fully cleared.")

    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if conn:
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    clear_test_steps()